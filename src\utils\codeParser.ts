import { Slide } from '@/types';

export function parseCodeBlocks(text: string): Slide[] {
  const slides: Slide[] = [];
  
  // Match HTML code blocks (```html...``` or ```...``` containing HTML)
  const codeBlockRegex = /```(?:html)?\s*([\s\S]*?)```/gi;
  const matches = text.matchAll(codeBlockRegex);
  
  let slideIndex = 0;
  
  for (const match of matches) {
    const codeContent = match[1]?.trim();
    
    if (codeContent && slideIndex < 10) { // Limit to 10 slides
      // Check if it looks like HTML content
      if (isHtmlContent(codeContent)) {
        slides.push({
          id: `slide-${slideIndex + 1}`,
          htmlContent: codeContent,
          codeBlock: codeContent
        });
        slideIndex++;
      }
    }
  }
  
  // If no code blocks found, try to extract HTML-like content directly
  if (slides.length === 0) {
    const htmlPattern = /<(?:html|div|section|article|main|header|footer|nav|aside)[^>]*>[\s\S]*?<\/(?:html|div|section|article|main|header|footer|nav|aside)>/gi;
    const htmlMatches = text.matchAll(htmlPattern);
    
    let directIndex = 0;
    for (const match of htmlMatches) {
      if (directIndex < 10) {
        slides.push({
          id: `slide-${directIndex + 1}`,
          htmlContent: match[0],
          codeBlock: match[0]
        });
        directIndex++;
      }
    }
  }
  
  return slides;
}

function isHtmlContent(content: string): boolean {
  // Check if content contains HTML tags
  const htmlTagRegex = /<[^>]+>/;
  return htmlTagRegex.test(content);
}

export function sanitizeHtml(html: string): string {
  // Basic sanitization - remove script tags and dangerous attributes
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
    .replace(/javascript:/gi, '');
}

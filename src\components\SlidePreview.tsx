import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Slide } from '@/types';
import { sanitizeHtml } from '@/utils/codeParser';

interface SlidePreviewProps {
  slide: Slide;
  index: number;
}

export default function SlidePreview({ slide, index }: SlidePreviewProps) {
  const [showCode, setShowCode] = useState(false);
  const [sanitizedHtml, setSanitizedHtml] = useState('');

  useEffect(() => {
    setSanitizedHtml(sanitizeHtml(slide.htmlContent));
  }, [slide.htmlContent]);

  return (
    <motion.div
      className="bg-white rounded-lg shadow-lg overflow-hidden"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
    >
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-800">
            Slide {index + 1}
          </h3>
          <div className="flex gap-2">
            <button
              onClick={() => setShowCode(!showCode)}
              className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded-md transition-colors"
            >
              {showCode ? 'Preview' : 'Code'}
            </button>
          </div>
        </div>
      </div>

      <div className="p-4">
        {showCode ? (
          <div className="bg-gray-900 rounded-lg p-4 overflow-auto max-h-96">
            <pre className="text-green-400 text-sm whitespace-pre-wrap">
              <code>{slide.codeBlock}</code>
            </pre>
          </div>
        ) : (
          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <div className="bg-gray-100 px-3 py-2 text-sm text-gray-600 border-b">
              Live Preview
            </div>
            <div 
              className="p-4 min-h-[300px] max-h-[500px] overflow-auto bg-white"
              dangerouslySetInnerHTML={{ __html: sanitizedHtml }}
            />
          </div>
        )}
      </div>

      <div className="bg-gray-50 px-4 py-3 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>Slide ID: {slide.id}</span>
          <span>{slide.htmlContent.length} characters</span>
        </div>
      </div>
    </motion.div>
  );
}

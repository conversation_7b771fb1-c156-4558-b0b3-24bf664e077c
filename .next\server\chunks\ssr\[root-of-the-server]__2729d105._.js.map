{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nextjs_project/src/components/ApiKeyInput.tsx"], "sourcesContent": ["import { useState } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface ApiKeyInputProps {\n  apiKey: string;\n  onApiKeyChange: (key: string) => void;\n}\n\nexport default function ApiKeyInput({ apiKey, onApiKeyChange }: ApiKeyInputProps) {\n  const [showKey, setShowKey] = useState(false);\n\n  return (\n    <motion.div\n      className=\"mb-6\"\n      initial={{ opacity: 0, y: -20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <label htmlFor=\"apiKey\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n        Gemini API Key\n      </label>\n      <div className=\"relative\">\n        <input\n          id=\"apiKey\"\n          type={showKey ? 'text' : 'password'}\n          value={apiKey}\n          onChange={(e) => onApiKeyChange(e.target.value)}\n          placeholder=\"Enter your Gemini API key...\"\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 pr-12\"\n        />\n        <button\n          type=\"button\"\n          onClick={() => setShowKey(!showKey)}\n          className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors\"\n        >\n          {showKey ? (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\" />\n            </svg>\n          ) : (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n            </svg>\n          )}\n        </button>\n      </div>\n      <p className=\"mt-2 text-sm text-gray-500\">\n        Get your API key from{' '}\n        <a\n          href=\"https://makersuite.google.com/app/apikey\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          className=\"text-blue-600 hover:text-blue-800 underline\"\n        >\n          Google AI Studio\n        </a>\n      </p>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOe,SAAS,YAAY,EAAE,MAAM,EAAE,cAAc,EAAoB;IAC9E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAE5B,8OAAC;gBAAM,SAAQ;gBAAS,WAAU;0BAA+C;;;;;;0BAGjF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,IAAG;wBACH,MAAM,UAAU,SAAS;wBACzB,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,aAAY;wBACZ,WAAU;;;;;;kCAEZ,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,WAAW,CAAC;wBAC3B,WAAU;kCAET,wBACC,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;iDAGvE,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;;8CACjE,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;8CACrE,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;0BAK7E,8OAAC;gBAAE,WAAU;;oBAA6B;oBAClB;kCACtB,8OAAC;wBACC,MAAK;wBACL,QAAO;wBACP,KAAI;wBACJ,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nextjs_project/src/components/TopicInput.tsx"], "sourcesContent": ["import { useState } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface TopicInputProps {\n  topic: string;\n  onTopicChange: (topic: string) => void;\n  onSubmit: () => void;\n  isLoading: boolean;\n  disabled: boolean;\n}\n\nexport default function TopicInput({ \n  topic, \n  onTopicChange, \n  onSubmit, \n  isLoading, \n  disabled \n}: TopicInputProps) {\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!disabled && topic.trim()) {\n      onSubmit();\n    }\n  };\n\n  return (\n    <motion.form\n      onSubmit={handleSubmit}\n      className=\"mb-8\"\n      initial={{ opacity: 0, y: -20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, delay: 0.2 }}\n    >\n      <label htmlFor=\"topic\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n        Presentation Topic\n      </label>\n      <div className=\"flex gap-3\">\n        <textarea\n          id=\"topic\"\n          value={topic}\n          onChange={(e) => onTopicChange(e.target.value)}\n          placeholder=\"Enter your presentation topic or content idea...\"\n          rows={3}\n          className=\"flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none\"\n          disabled={isLoading}\n        />\n        <motion.button\n          type=\"submit\"\n          disabled={disabled || !topic.trim()}\n          className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium\"\n          whileHover={{ scale: disabled ? 1 : 1.02 }}\n          whileTap={{ scale: disabled ? 1 : 0.98 }}\n        >\n          {isLoading ? (\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n              <span>Generating...</span>\n            </div>\n          ) : (\n            'Generate Slides'\n          )}\n        </motion.button>\n      </div>\n      <p className=\"mt-2 text-sm text-gray-500\">\n        Describe your presentation topic and we'll generate up to 10 slides for you.\n      </p>\n    </motion.form>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAUe,SAAS,WAAW,EACjC,KAAK,EACL,aAAa,EACb,QAAQ,EACR,SAAS,EACT,QAAQ,EACQ;IAChB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,YAAY,MAAM,IAAI,IAAI;YAC7B;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;QACV,UAAU;QACV,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO;QAAI;;0BAExC,8OAAC;gBAAM,SAAQ;gBAAQ,WAAU;0BAA+C;;;;;;0BAGhF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,IAAG;wBACH,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,aAAY;wBACZ,MAAM;wBACN,WAAU;wBACV,UAAU;;;;;;kCAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,MAAK;wBACL,UAAU,YAAY,CAAC,MAAM,IAAI;wBACjC,WAAU;wBACV,YAAY;4BAAE,OAAO,WAAW,IAAI;wBAAK;wBACzC,UAAU;4BAAE,OAAO,WAAW,IAAI;wBAAK;kCAEtC,0BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;8CAAK;;;;;;;;;;;mCAGR;;;;;;;;;;;;0BAIN,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAKhD", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nextjs_project/src/utils/codeParser.ts"], "sourcesContent": ["import { Slide } from '@/types';\n\nexport function parseCodeBlocks(text: string): Slide[] {\n  const slides: Slide[] = [];\n  \n  // Match HTML code blocks (```html...``` or ```...``` containing HTML)\n  const codeBlockRegex = /```(?:html)?\\s*([\\s\\S]*?)```/gi;\n  const matches = text.matchAll(codeBlockRegex);\n  \n  let slideIndex = 0;\n  \n  for (const match of matches) {\n    const codeContent = match[1]?.trim();\n    \n    if (codeContent && slideIndex < 10) { // Limit to 10 slides\n      // Check if it looks like HTML content\n      if (isHtmlContent(codeContent)) {\n        slides.push({\n          id: `slide-${slideIndex + 1}`,\n          htmlContent: codeContent,\n          codeBlock: codeContent\n        });\n        slideIndex++;\n      }\n    }\n  }\n  \n  // If no code blocks found, try to extract HTML-like content directly\n  if (slides.length === 0) {\n    const htmlPattern = /<(?:html|div|section|article|main|header|footer|nav|aside)[^>]*>[\\s\\S]*?<\\/(?:html|div|section|article|main|header|footer|nav|aside)>/gi;\n    const htmlMatches = text.matchAll(htmlPattern);\n    \n    let directIndex = 0;\n    for (const match of htmlMatches) {\n      if (directIndex < 10) {\n        slides.push({\n          id: `slide-${directIndex + 1}`,\n          htmlContent: match[0],\n          codeBlock: match[0]\n        });\n        directIndex++;\n      }\n    }\n  }\n  \n  return slides;\n}\n\nfunction isHtmlContent(content: string): boolean {\n  // Check if content contains HTML tags\n  const htmlTagRegex = /<[^>]+>/;\n  return htmlTagRegex.test(content);\n}\n\nexport function sanitizeHtml(html: string): string {\n  // Basic sanitization - remove script tags and dangerous attributes\n  return html\n    .replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n    .replace(/on\\w+\\s*=\\s*[\"'][^\"']*[\"']/gi, '')\n    .replace(/javascript:/gi, '');\n}\n"], "names": [], "mappings": ";;;;AAEO,SAAS,gBAAgB,IAAY;IAC1C,MAAM,SAAkB,EAAE;IAE1B,sEAAsE;IACtE,MAAM,iBAAiB;IACvB,MAAM,UAAU,KAAK,QAAQ,CAAC;IAE9B,IAAI,aAAa;IAEjB,KAAK,MAAM,SAAS,QAAS;QAC3B,MAAM,cAAc,KAAK,CAAC,EAAE,EAAE;QAE9B,IAAI,eAAe,aAAa,IAAI;YAClC,sCAAsC;YACtC,IAAI,cAAc,cAAc;gBAC9B,OAAO,IAAI,CAAC;oBACV,IAAI,CAAC,MAAM,EAAE,aAAa,GAAG;oBAC7B,aAAa;oBACb,WAAW;gBACb;gBACA;YACF;QACF;IACF;IAEA,qEAAqE;IACrE,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,MAAM,cAAc;QACpB,MAAM,cAAc,KAAK,QAAQ,CAAC;QAElC,IAAI,cAAc;QAClB,KAAK,MAAM,SAAS,YAAa;YAC/B,IAAI,cAAc,IAAI;gBACpB,OAAO,IAAI,CAAC;oBACV,IAAI,CAAC,MAAM,EAAE,cAAc,GAAG;oBAC9B,aAAa,KAAK,CAAC,EAAE;oBACrB,WAAW,KAAK,CAAC,EAAE;gBACrB;gBACA;YACF;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS,cAAc,OAAe;IACpC,sCAAsC;IACtC,MAAM,eAAe;IACrB,OAAO,aAAa,IAAI,CAAC;AAC3B;AAEO,SAAS,aAAa,IAAY;IACvC,mEAAmE;IACnE,OAAO,KACJ,OAAO,CAAC,uDAAuD,IAC/D,OAAO,CAAC,gCAAgC,IACxC,OAAO,CAAC,iBAAiB;AAC9B", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nextjs_project/src/components/SlidePreview.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Slide } from '@/types';\nimport { sanitizeHtml } from '@/utils/codeParser';\n\ninterface SlidePreviewProps {\n  slide: Slide;\n  index: number;\n}\n\nexport default function SlidePreview({ slide, index }: SlidePreviewProps) {\n  const [showCode, setShowCode] = useState(false);\n  const [sanitizedHtml, setSanitizedHtml] = useState('');\n\n  useEffect(() => {\n    setSanitizedHtml(sanitizeHtml(slide.htmlContent));\n  }, [slide.htmlContent]);\n\n  return (\n    <motion.div\n      className=\"bg-white rounded-lg shadow-lg overflow-hidden\"\n      initial={{ opacity: 0, y: 50 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, delay: index * 0.1 }}\n    >\n      <div className=\"bg-gray-50 px-4 py-3 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-gray-800\">\n            Slide {index + 1}\n          </h3>\n          <div className=\"flex gap-2\">\n            <button\n              onClick={() => setShowCode(!showCode)}\n              className=\"px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded-md transition-colors\"\n            >\n              {showCode ? 'Preview' : 'Code'}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-4\">\n        {showCode ? (\n          <div className=\"bg-gray-900 rounded-lg p-4 overflow-auto max-h-96\">\n            <pre className=\"text-green-400 text-sm whitespace-pre-wrap\">\n              <code>{slide.codeBlock}</code>\n            </pre>\n          </div>\n        ) : (\n          <div className=\"border border-gray-200 rounded-lg overflow-hidden\">\n            <div className=\"bg-gray-100 px-3 py-2 text-sm text-gray-600 border-b\">\n              Live Preview\n            </div>\n            <div \n              className=\"p-4 min-h-[300px] max-h-[500px] overflow-auto bg-white\"\n              dangerouslySetInnerHTML={{ __html: sanitizedHtml }}\n            />\n          </div>\n        )}\n      </div>\n\n      <div className=\"bg-gray-50 px-4 py-3 border-t border-gray-200\">\n        <div className=\"flex items-center justify-between text-sm text-gray-600\">\n          <span>Slide ID: {slide.id}</span>\n          <span>{slide.htmlContent.length} characters</span>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAOe,SAAS,aAAa,EAAE,KAAK,EAAE,KAAK,EAAqB;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iBAAiB,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,MAAM,WAAW;IACjD,GAAG;QAAC,MAAM,WAAW;KAAC;IAEtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAI;;0BAEhD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAsC;gCAC3C,QAAQ;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,YAAY,CAAC;gCAC5B,WAAU;0CAET,WAAW,YAAY;;;;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC;gBAAI,WAAU;0BACZ,yBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAM,MAAM,SAAS;;;;;;;;;;;;;;;yCAI1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAuD;;;;;;sCAGtE,8OAAC;4BACC,WAAU;4BACV,yBAAyB;gCAAE,QAAQ;4BAAc;;;;;;;;;;;;;;;;;0BAMzD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;gCAAK;gCAAW,MAAM,EAAE;;;;;;;sCACzB,8OAAC;;gCAAM,MAAM,WAAW,CAAC,MAAM;gCAAC;;;;;;;;;;;;;;;;;;;;;;;;AAK1C", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nextjs_project/src/components/LoadingSpinner.tsx"], "sourcesContent": ["import { motion } from 'framer-motion';\n\nexport default function LoadingSpinner() {\n  return (\n    <div className=\"flex flex-col items-center justify-center py-8\">\n      <motion.div\n        className=\"w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full\"\n        animate={{ rotate: 360 }}\n        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n      />\n      <motion.p\n        className=\"mt-4 text-gray-600\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.5 }}\n      >\n        Generating slides with Gemini AI...\n      </motion.p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAS;;;;;;0BAE9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;0BAC1B;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nextjs_project/src/utils/geminiApi.ts"], "sourcesContent": ["import { GeminiRequest, GeminiResponse, ApiError } from '@/types';\n\nconst GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';\n\nexport async function generateSlides(topic: string, apiKey: string): Promise<string> {\n  if (!apiKey.trim()) {\n    throw new Error('API key is required');\n  }\n\n  if (!topic.trim()) {\n    throw new Error('Topic is required');\n  }\n\n  const prompt = `You are an HTML/CSS/JS slide generator. Generate up to 10 slides based on the following topic. Each slide must be a single HTML/CSS/JS code block (all inline). Only return code blocks, no explanations.\n\nTopic: ${topic}`;\n\n  const requestBody: GeminiRequest = {\n    contents: [\n      {\n        parts: [\n          {\n            text: prompt\n          }\n        ]\n      }\n    ]\n  };\n\n  try {\n    const response = await fetch(`${GEMINI_API_URL}?key=${apiKey}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(requestBody),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(\n        errorData.error?.message || \n        `API request failed with status ${response.status}`\n      );\n    }\n\n    const data: GeminiResponse = await response.json();\n    \n    if (!data.candidates || data.candidates.length === 0) {\n      throw new Error('No response generated from Gemini API');\n    }\n\n    const generatedText = data.candidates[0]?.content?.parts?.[0]?.text;\n    \n    if (!generatedText) {\n      throw new Error('Empty response from Gemini API');\n    }\n\n    return generatedText;\n  } catch (error) {\n    if (error instanceof Error) {\n      throw error;\n    }\n    throw new Error('Failed to generate slides');\n  }\n}\n"], "names": [], "mappings": ";;;AAEA,MAAM,iBAAiB;AAEhB,eAAe,eAAe,KAAa,EAAE,MAAc;IAChE,IAAI,CAAC,OAAO,IAAI,IAAI;QAClB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,CAAC,MAAM,IAAI,IAAI;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,SAAS,CAAC;;OAEX,EAAE,OAAO;IAEd,MAAM,cAA6B;QACjC,UAAU;YACR;gBACE,OAAO;oBACL;wBACE,MAAM;oBACR;iBACD;YACH;SACD;IACH;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,KAAK,EAAE,QAAQ,EAAE;YAC9D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MACR,UAAU,KAAK,EAAE,WACjB,CAAC,+BAA+B,EAAE,SAAS,MAAM,EAAE;QAEvD;QAEA,MAAM,OAAuB,MAAM,SAAS,IAAI;QAEhD,IAAI,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,GAAG;YACpD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,gBAAgB,KAAK,UAAU,CAAC,EAAE,EAAE,SAAS,OAAO,CAAC,EAAE,EAAE;QAE/D,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,OAAO;YAC1B,MAAM;QACR;QACA,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nextjs_project/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport ApiKeyInput from '@/components/ApiKeyInput';\nimport TopicInput from '@/components/TopicInput';\nimport SlidePreview from '@/components/SlidePreview';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport { generateSlides } from '@/utils/geminiApi';\nimport { parseCodeBlocks } from '@/utils/codeParser';\nimport { Slide } from '@/types';\n\nexport default function Home() {\n  const [apiKey, setApiKey] = useState('');\n  const [topic, setTopic] = useState('');\n  const [slides, setSlides] = useState<Slide[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleGenerateSlides = async () => {\n    if (!apiKey.trim() || !topic.trim()) {\n      setError('Please provide both API key and topic');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n    setSlides([]);\n\n    try {\n      const response = await generateSlides(topic, apiKey);\n      const parsedSlides = parseCodeBlocks(response);\n\n      if (parsedSlides.length === 0) {\n        setError('No valid slides were generated. Please try a different topic.');\n      } else {\n        setSlides(parsedSlides);\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to generate slides');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"container mx-auto px-4 py-8 max-w-6xl\">\n        {/* Header */}\n        <motion.div\n          className=\"text-center mb-12\"\n          initial={{ opacity: 0, y: -30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n        >\n          <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">\n            AI Slide Generator\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Create beautiful presentation slides instantly using Google's Gemini AI.\n            Just enter your topic and watch the magic happen!\n          </p>\n        </motion.div>\n\n        {/* Input Section */}\n        <div className=\"bg-white rounded-xl shadow-lg p-6 md:p-8 mb-8\">\n          <ApiKeyInput apiKey={apiKey} onApiKeyChange={setApiKey} />\n          <TopicInput\n            topic={topic}\n            onTopicChange={setTopic}\n            onSubmit={handleGenerateSlides}\n            isLoading={isLoading}\n            disabled={!apiKey.trim() || isLoading}\n          />\n        </div>\n\n        {/* Error Display */}\n        {error && (\n          <motion.div\n            className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-8\"\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.3 }}\n          >\n            <div className=\"flex items-center\">\n              <svg className=\"w-5 h-5 text-red-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n              </svg>\n              <p className=\"text-red-700\">{error}</p>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Loading State */}\n        {isLoading && <LoadingSpinner />}\n\n        {/* Slides Display */}\n        {slides.length > 0 && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.5 }}\n          >\n            <div className=\"flex items-center justify-between mb-6\">\n              <h2 className=\"text-2xl font-bold text-gray-900\">\n                Generated Slides ({slides.length})\n              </h2>\n              <button\n                onClick={() => setSlides([])}\n                className=\"px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                Clear All\n              </button>\n            </div>\n\n            <div className=\"grid gap-8\">\n              {slides.map((slide, index) => (\n                <SlidePreview key={slide.id} slide={slide} index={index} />\n              ))}\n            </div>\n          </motion.div>\n        )}\n\n        {/* Footer */}\n        <motion.footer\n          className=\"mt-16 text-center text-gray-500\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 1, duration: 0.5 }}\n        >\n          <p>Powered by Google Gemini AI • Built with Next.js & Tailwind CSS</p>\n        </motion.footer>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,uBAAuB;QAC3B,IAAI,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI;YACnC,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QACT,UAAU,EAAE;QAEZ,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAC7C,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE;YAErC,IAAI,aAAa,MAAM,KAAK,GAAG;gBAC7B,SAAS;YACX,OAAO;gBACL,UAAU;YACZ;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,UAAW;4BAAC,QAAQ;4BAAQ,gBAAgB;;;;;;sCAC7C,8OAAC,gIAAA,CAAA,UAAU;4BACT,OAAO;4BACP,eAAe;4BACf,UAAU;4BACV,WAAW;4BACX,UAAU,CAAC,OAAO,IAAI,MAAM;;;;;;;;;;;;gBAK/B,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAK;oBACnC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAA4B,MAAK;gCAAe,SAAQ;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;;0CAEhQ,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;gBAMlC,2BAAa,8OAAC,oIAAA,CAAA,UAAc;;;;;gBAG5B,OAAO,MAAM,GAAG,mBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAmC;wCAC5B,OAAO,MAAM;wCAAC;;;;;;;8CAEnC,8OAAC;oCACC,SAAS,IAAM,UAAU,EAAE;oCAC3B,WAAU;8CACX;;;;;;;;;;;;sCAKH,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,kIAAA,CAAA,UAAY;oCAAgB,OAAO;oCAAO,OAAO;mCAA/B,MAAM,EAAE;;;;;;;;;;;;;;;;8BAOnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;wBAAG,UAAU;oBAAI;8BAEtC,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}]}
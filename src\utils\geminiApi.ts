import { GeminiRequest, GeminiResponse, ApiError } from '@/types';

const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

export async function generateSlides(topic: string, apiKey: string): Promise<string> {
  if (!apiKey.trim()) {
    throw new Error('API key is required');
  }

  if (!topic.trim()) {
    throw new Error('Topic is required');
  }

  const prompt = `You are an HTML/CSS/JS slide generator. Generate up to 10 slides based on the following topic. Each slide must be a single HTML/CSS/JS code block (all inline). Only return code blocks, no explanations.

Topic: ${topic}`;

  const requestBody: GeminiRequest = {
    contents: [
      {
        parts: [
          {
            text: prompt
          }
        ]
      }
    ]
  };

  try {
    const response = await fetch(`${GEMINI_API_URL}?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error?.message || 
        `API request failed with status ${response.status}`
      );
    }

    const data: GeminiResponse = await response.json();
    
    if (!data.candidates || data.candidates.length === 0) {
      throw new Error('No response generated from Gemini API');
    }

    const generatedText = data.candidates[0]?.content?.parts?.[0]?.text;
    
    if (!generatedText) {
      throw new Error('Empty response from Gemini API');
    }

    return generatedText;
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to generate slides');
  }
}

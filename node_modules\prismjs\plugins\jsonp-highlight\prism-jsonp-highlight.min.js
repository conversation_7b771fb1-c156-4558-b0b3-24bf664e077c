!function(){if("undefined"!=typeof Prism&&"undefined"!=typeof document){var t=[];o((function(t){if(t&&t.meta&&t.data){if(t.meta.status&&t.meta.status>=400)return"Error: "+(t.data.message||t.meta.status);if("string"==typeof t.data.content)return"function"==typeof atob?atob(t.data.content.replace(/\s/g,"")):"Your browser cannot decode base64"}return null}),"github"),o((function(t,e){if(t&&t.meta&&t.data&&t.data.files){if(t.meta.status&&t.meta.status>=400)return"Error: "+(t.data.message||t.meta.status);var n=t.data.files,a=e.getAttribute("data-filename");if(null==a)for(var r in n)if(n.hasOwnProperty(r)){a=r;break}return void 0!==n[a]?n[a].content:"Error: unknown or missing gist file "+a}return null}),"gist"),o((function(t){return t&&t.node&&"string"==typeof t.data?t.data:null}),"bitbucket");var e=0,n="data-jsonp-status",a="failed",r='pre[data-jsonp]:not([data-jsonp-status="loaded"]):not([data-jsonp-status="loading"])';Prism.hooks.add("before-highlightall",(function(t){t.selector+=", "+r})),Prism.hooks.add("before-sanity-check",(function(o){var i,u=o.element;if(u.matches(r)){o.code="",u.setAttribute(n,"loading");var s=u.appendChild(document.createElement("CODE"));s.textContent="Loading…";var d=o.language;s.className="language-"+d;var f=Prism.plugins.autoloader;f&&f.loadLanguages(d);var l=u.getAttribute("data-adapter"),c=null;if(l){if("function"!=typeof window[l])return u.setAttribute(n,a),void(s.textContent=(i=l,'✖ Error: JSONP adapter function "'+i+"\" doesn't exist"));c=window[l]}var p=u.getAttribute("data-jsonp");!function(r,o,i,d){var f="prismjsonp"+e++,l=document.createElement("a");l.href=r,l.href+=(l.search?"&":"?")+(o||"callback")+"="+f;var p=document.createElement("script");p.src=l.href,p.onerror=function(){g(),d()};var m=setTimeout((function(){g(),d()}),Prism.plugins.jsonphighlight.timeout);function g(){clearTimeout(m),document.head.removeChild(p),delete window[f]}window[f]=function(e){g(),function(e){var r=null;if(c)r=c(e,u);else for(var o=0,i=t.length;o<i&&null===(r=t[o].adapter(e,u));o++);null===r?(u.setAttribute(n,a),s.textContent="✖ Error: Cannot parse response (perhaps you need an adapter function?)"):(u.setAttribute(n,"loaded"),s.textContent=r,Prism.highlightElement(s))}(e)},document.head.appendChild(p)}(p,u.getAttribute("data-callback"),0,(function(){u.setAttribute(n,a),s.textContent="✖ Error: Timeout loading "+p}))}})),Prism.plugins.jsonphighlight={timeout:5e3,registerAdapter:o,removeAdapter:function(e){if("string"==typeof e&&(e=i(e)),"function"==typeof e){var n=t.findIndex((function(t){return t.adapter===e}));n>=0&&t.splice(n,1)}},highlight:function(t){for(var e,n=(t||document).querySelectorAll(r),a=0;e=n[a++];)Prism.highlightElement(e)}}}function o(e,n){n=n||e.name,"function"!=typeof e||i(e)||i(n)||t.push({adapter:e,name:n})}function i(e){if("function"==typeof e){for(var n=0;a=t[n++];)if(a.adapter.valueOf()===e.valueOf())return a.adapter}else if("string"==typeof e){var a;for(n=0;a=t[n++];)if(a.name===e)return a.adapter}return null}}();
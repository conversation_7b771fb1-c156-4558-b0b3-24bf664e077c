'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import ApiKeyInput from '@/components/ApiKeyInput';
import TopicInput from '@/components/TopicInput';
import SlidePreview from '@/components/SlidePreview';
import LoadingSpinner from '@/components/LoadingSpinner';
import { generateSlides } from '@/utils/geminiApi';
import { parseCodeBlocks } from '@/utils/codeParser';
import { Slide } from '@/types';

export default function Home() {
  const [apiKey, setApiKey] = useState('');
  const [topic, setTopic] = useState('');
  const [slides, setSlides] = useState<Slide[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGenerateSlides = async () => {
    if (!apiKey.trim() || !topic.trim()) {
      setError('Please provide both API key and topic');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSlides([]);

    try {
      const response = await generateSlides(topic, apiKey);
      const parsedSlides = parseCodeBlocks(response);

      if (parsedSlides.length === 0) {
        setError('No valid slides were generated. Please try a different topic.');
      } else {
        setSlides(parsedSlides);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate slides');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            AI Slide Generator
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Create beautiful presentation slides instantly using Google's Gemini AI.
            Just enter your topic and watch the magic happen!
          </p>
        </motion.div>

        {/* Input Section */}
        <div className="bg-white rounded-xl shadow-lg p-6 md:p-8 mb-8">
          <ApiKeyInput apiKey={apiKey} onApiKeyChange={setApiKey} />
          <TopicInput
            topic={topic}
            onTopicChange={setTopic}
            onSubmit={handleGenerateSlides}
            isLoading={isLoading}
            disabled={!apiKey.trim() || isLoading}
          />
        </div>

        {/* Error Display */}
        {error && (
          <motion.div
            className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <p className="text-red-700">{error}</p>
            </div>
          </motion.div>
        )}

        {/* Loading State */}
        {isLoading && <LoadingSpinner />}

        {/* Slides Display */}
        {slides.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                Generated Slides ({slides.length})
              </h2>
              <button
                onClick={() => setSlides([])}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Clear All
              </button>
            </div>

            <div className="grid gap-8">
              {slides.map((slide, index) => (
                <SlidePreview key={slide.id} slide={slide} index={index} />
              ))}
            </div>
          </motion.div>
        )}

        {/* Footer */}
        <motion.footer
          className="mt-16 text-center text-gray-500"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 0.5 }}
        >
          <p>Powered by Google Gemini AI • Built with Next.js & Tailwind CSS</p>
        </motion.footer>
      </div>
    </div>
  );
}

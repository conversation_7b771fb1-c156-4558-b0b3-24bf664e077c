import { useState } from 'react';
import { motion } from 'framer-motion';

interface TopicInputProps {
  topic: string;
  onTopicChange: (topic: string) => void;
  onSubmit: () => void;
  isLoading: boolean;
  disabled: boolean;
}

export default function TopicInput({ 
  topic, 
  onTopicChange, 
  onSubmit, 
  isLoading, 
  disabled 
}: TopicInputProps) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!disabled && topic.trim()) {
      onSubmit();
    }
  };

  return (
    <motion.form
      onSubmit={handleSubmit}
      className="mb-8"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      <label htmlFor="topic" className="block text-sm font-medium text-gray-700 mb-2">
        Presentation Topic
      </label>
      <div className="flex gap-3">
        <textarea
          id="topic"
          value={topic}
          onChange={(e) => onTopicChange(e.target.value)}
          placeholder="Enter your presentation topic or content idea..."
          rows={3}
          className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
          disabled={isLoading}
        />
        <motion.button
          type="submit"
          disabled={disabled || !topic.trim()}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium"
          whileHover={{ scale: disabled ? 1 : 1.02 }}
          whileTap={{ scale: disabled ? 1 : 0.98 }}
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              <span>Generating...</span>
            </div>
          ) : (
            'Generate Slides'
          )}
        </motion.button>
      </div>
      <p className="mt-2 text-sm text-gray-500">
        Describe your presentation topic and we'll generate up to 10 slides for you.
      </p>
    </motion.form>
  );
}

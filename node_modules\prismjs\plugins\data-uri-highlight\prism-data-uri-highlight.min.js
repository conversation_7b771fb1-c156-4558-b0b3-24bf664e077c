!function(){if("undefined"!=typeof Prism){var i={pattern:/(.)\bdata:[^\/]+\/[^,]+,(?:(?!\1)[\s\S]|\\\1)+(?=\1)/,lookbehind:!0,inside:{"language-css":{pattern:/(data:[^\/]+\/(?:[^+,]+\+)?css,)[\s\S]+/,lookbehind:!0},"language-javascript":{pattern:/(data:[^\/]+\/(?:[^+,]+\+)?javascript,)[\s\S]+/,lookbehind:!0},"language-json":{pattern:/(data:[^\/]+\/(?:[^+,]+\+)?json,)[\s\S]+/,lookbehind:!0},"language-markup":{pattern:/(data:[^\/]+\/(?:[^+,]+\+)?(?:html|xml),)[\s\S]+/,lookbehind:!0}}},a=["url","attr-value","string"];Prism.plugins.dataURIHighlight={processGrammar:function(n){n&&!n["data-uri"]&&(Prism.languages.DFS(n,(function(n,r,e){a.indexOf(e)>-1&&!Array.isArray(r)&&(r.pattern||(r=this[n]={pattern:r}),r.inside=r.inside||{},"attr-value"==e?Prism.languages.insertBefore("inside",r.inside["url-link"]?"url-link":"punctuation",{"data-uri":i},r):r.inside["url-link"]?Prism.languages.insertBefore("inside","url-link",{"data-uri":i},r):r.inside["data-uri"]=i)})),n["data-uri"]=i)}},Prism.hooks.add("before-highlight",(function(a){if(i.pattern.test(a.code))for(var n in i.inside)if(i.inside.hasOwnProperty(n)&&!i.inside[n].inside&&i.inside[n].pattern.test(a.code)){var r=n.match(/^language-(.+)/)[1];Prism.languages[r]&&(i.inside[n].inside={rest:(e=Prism.languages[r],Prism.plugins.autolinker&&Prism.plugins.autolinker.processGrammar(e),e)})}var e;Prism.plugins.dataURIHighlight.processGrammar(a.grammar)}))}}();